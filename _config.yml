# Jekyll configuration for GitHub Pages
title: Personal Blog
description: A minimalist personal blog
author: Your Name
email: <EMAIL>

# Theme
theme: jekyll-theme-dinky

# Permalink structure for nice URLs
permalink: /:year/:month/:day/:title/

# Collections
collections:
  posts:
    output: true

# Defaults
defaults:
  - scope:
      path: ""
      type: "posts"
    values:
      layout: "post"
  - scope:
      path: ""
      type: "pages"
    values:
      layout: "page"

# Build settings
markdown: kramdown
kramdown:
  input: GFM
  hard_wrap: false

# Exclude files from build
exclude:
  - Gemfile
  - Gemfile.lock
  - README.md
  - LICENSE